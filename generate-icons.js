// This is a simple Node.js script to generate placeholder icons
// You can run this with Node.js to generate the icons

const fs = require('fs');
const path = require('path');

// Function to generate a simple SVG icon
function generateSVG(size) {
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#4a6cf7"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size/3}" fill="white"/>
  <text x="${size/2}" y="${size/2 + size/10}" font-family="Arial" font-size="${size/3}" 
    text-anchor="middle" fill="#4a6cf7">S</text>
</svg>`;
}

// Create the icons directory if it doesn't exist
const iconsDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir);
}

// Generate icons of different sizes
const sizes = [16, 48, 128];
sizes.forEach(size => {
  const svg = generateSVG(size);
  fs.writeFileSync(path.join(iconsDir, `icon${size}.svg`), svg);
  console.log(`Generated icon${size}.svg`);
});

console.log('Icon generation complete. Convert these SVGs to PNGs for use in the extension.');
