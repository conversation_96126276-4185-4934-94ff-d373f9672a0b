# Page Summarization Extension

A Chrome extension that summarizes web pages using OpenAI's GPT-4.1 model and LangChain.

## Features

- Extracts content from web pages
- Summarizes content into 10 concise bullet points using OpenAI's GPT-4.1 model
- Uses Lang<PERSON>hain for structured prompting and response parsing
- Rotates user agents to avoid rate limiting
- Stores OpenAI API key securely in local storage
- Clean and intuitive user interface

## Installation

### Development Mode

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/page-summarization-extension.git
   ```

2. Open Chrome and navigate to `chrome://extensions/`

3. Enable "Developer mode" by toggling the switch in the top right corner

4. Click "Load unpacked" and select the directory containing the extension files

5. The extension should now be installed and visible in your Chrome toolbar

## Usage

1. Click on the extension icon in your Chrome toolbar to open the popup

2. If you haven't already, enter your OpenAI API key and click "Save"

3. Navigate to a web page you want to summarize

4. Click "Summarize This Page" in the extension popup

5. Wait for the summary to be generated (this may take a few seconds)

6. View the 10-bullet point summary of the page content

7. Use the "Copy to Clipboard" button to copy the summary

## Requirements

- Chrome browser (version 88 or later)
- OpenAI API key with access to GPT-4.1 model

## Technologies Used

- JavaScript
- Chrome Extension API
- LangChain
- OpenAI API

## License

MIT

## Acknowledgements

- OpenAI for providing the GPT-4.1 model
- LangChain for the structured prompting framework
