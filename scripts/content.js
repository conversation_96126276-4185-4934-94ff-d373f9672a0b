// Content script to extract page content

// Listen for messages from the popup or background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "extractContent") {
    const pageContent = extractPageContent();
    sendResponse({ content: pageContent });
  }
  return true; // Required for async response
});

// Function to extract the main content from the page
function extractPageContent() {
  // Get the page title
  const title = document.title;
  
  // Try to get the main content
  // First, look for article elements
  let content = "";
  const articleElements = document.querySelectorAll('article');
  
  if (articleElements.length > 0) {
    // Use the first article element
    content = articleElements[0].innerText;
  } else {
    // If no article elements, try to find the main content
    const mainElement = document.querySelector('main');
    if (mainElement) {
      content = mainElement.innerText;
    } else {
      // If no main element, try to find the content based on common content containers
      const contentContainers = document.querySelectorAll('.content, .post, .entry, .article, #content, #main');
      if (contentContainers.length > 0) {
        content = contentContainers[0].innerText;
      } else {
        // If all else fails, just get the body text
        // Remove script, style, and other non-content elements
        const bodyClone = document.body.cloneNode(true);
        const elementsToRemove = bodyClone.querySelectorAll('script, style, nav, header, footer, aside, [role="banner"], [role="navigation"], [role="complementary"]');
        elementsToRemove.forEach(el => el.remove());
        content = bodyClone.innerText;
      }
    }
  }
  
  // Clean up the content (remove extra whitespace)
  content = content.replace(/\s+/g, ' ').trim();
  
  return {
    title,
    url: window.location.href,
    content
  };
}
