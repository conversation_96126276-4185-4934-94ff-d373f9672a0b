// User agents for randomization
const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0'
];

// Function to get random user agent
function getRandomUserAgent() {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

// Function to summarize content using OpenAI API directly
async function summarizeContent(content, apiKey) {
  try {
    if (!apiKey) {
      throw new Error("OpenAI API key is required");
    }

    const prompt = `You are a helpful assistant that summarizes web content.

Below is the content from a webpage:
Title: ${content.title}
URL: ${content.url}
Content: ${content.content}

Please provide a concise summary of this content in exactly 10 bullet points.
Each bullet point should capture a key insight or piece of information from the content.
Make the bullet points informative and comprehensive.

Format your response as a list of 10 bullet points, each starting with a "•" character.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'User-Agent': getRandomUserAgent()
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error("Error summarizing content:", error);
    throw error;
  }
}

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "summarize") {
    handleSummarizeRequest(request.tabId, request.apiKey)
      .then(summary => sendResponse({ success: true, summary }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Required for async response
  }
});

// Function to handle summarization requests
async function handleSummarizeRequest(tabId, apiKey) {
  try {
    // Extract content from the active tab
    const content = await extractContentFromTab(tabId);

    // Summarize the content using OpenAI API
    const summary = await summarizeContent(content, apiKey);

    return summary;
  } catch (error) {
    console.error("Error in handleSummarizeRequest:", error);
    throw error;
  }
}

// Function to extract content from a tab
async function extractContentFromTab(tabId) {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(tabId, { action: "extractContent" }, response => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else if (!response) {
        reject(new Error("No response from content script"));
      } else {
        resolve(response.content);
      }
    });
  });
}
